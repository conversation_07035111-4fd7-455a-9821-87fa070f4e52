# Tailwind CSS Setup and Troubleshooting

## Current Configuration

This project is configured with Tailwind CSS and should properly recognize all Tailwind directives including `@tailwind`, `@apply`, `@layer`, etc.

### Files Configured:

1. **`.vscode/settings.json`** - VS Code workspace settings
2. **`.vscode/css_custom_data.json`** - Custom CSS data for Tailwind directives
3. **`.vscode/extensions.json`** - Recommended extensions
4. **`postcss.config.js`** - PostCSS configuration
5. **`.postcssrc.json`** - Alternative PostCSS configuration
6. **`tailwind.config.js`** - Tailwind configuration

## Troubleshooting "Unknown at rule" Errors

If you're still seeing "Unknown at rule @tailwind" or "@apply" errors, try these steps:

### Step 1: Reload VS Code Window
1. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "Developer: Reload Window"
3. Press Enter

### Step 2: Restart CSS Language Server
1. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "TypeScript: Restart TS Server"
3. Press Enter

### Step 3: Install Recommended Extensions
Make sure you have the Tailwind CSS IntelliSense extension installed:
1. Press `Ctrl+Shift+X` (or `Cmd+Shift+X` on Mac)
2. Search for "Tailwind CSS IntelliSense"
3. Install the extension by Brad Cornes

### Step 4: Check File Association
Ensure your CSS files are properly associated:
1. Open your `index.css` file
2. Look at the bottom right corner of VS Code
3. It should show "CSS" or "Tailwind CSS"
4. If not, click on it and select "CSS"

### Step 5: Clear VS Code Cache (Last Resort)
If nothing else works:
1. Close VS Code completely
2. Delete the `.vscode` folder temporarily
3. Restart VS Code
4. Restore the `.vscode` folder

## Verification

Your CSS should work properly with these directives:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded;
  }
}
```

## Build Process

The build process should work correctly with:
```bash
npm run dev
npm run build
```

Tailwind will process all directives during the build process, even if VS Code shows warnings.

{"css.validate": false, "less.validate": false, "scss.validate": false, "css.lint.unknownAtRules": "ignore", "css.lint.invalidApply": "ignore", "css.customData": [".vscode/css_custom_data.json"], "tailwindCSS.includeLanguages": {"css": "css", "scss": "scss"}, "tailwindCSS.experimental.classRegex": [["@apply\\s+([^;]+);", 1]], "editor.quickSuggestions": {"strings": true}, "emmet.includeLanguages": {"css": "css"}, "[css]": {"editor.suggest.insertMode": "replace", "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}}}
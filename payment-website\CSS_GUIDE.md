# PayShop CSS Guide

## Overview
This project now uses a single, comprehensive CSS file (`src/index.css`) instead of Tailwind CSS. The CSS is organized in a human-readable way with simple class names for easy customization.

## CSS Structure

### 1. CSS Variables (Lines 6-54)
All colors, spacing, shadows, and other design tokens are defined as CSS variables for easy customization:

```css
:root {
  /* Colors */
  --primary-blue: #667eea;
  --primary-purple: #764ba2;
  --azure-blue: #0ea5e9;
  /* ... more variables */
}
```

### 2. Global Reset (Lines 56-123)
Basic reset styles and global element styling.

### 3. Component Styles

#### Buttons (Lines 125-176)
- `.btn-primary` - Main action buttons
- `.btn-secondary` - Secondary buttons  
- `.btn-azure` - Special azure buttons with blinking effect

#### Cards (Lines 178-207)
- `.card-premium` - Premium style cards
- `.card-product` - Product display cards

#### Layout Classes (Lines 270-338)
- `.container` - Main content container
- `.flex`, `.flex-col` - Flexbox utilities
- `.min-h-screen` - Full height
- Various positioning and spacing utilities

#### Component-Specific Styles
- **Navbar** (Lines 566-733) - Navigation bar styling
- **Home Page** (Lines 735-848) - Hero section and home page
- **Shop Page** (Lines 850-1004) - Product grid and cards
- **Cart Page** (Lines 1006-1279) - Shopping cart interface
- **Checkout Page** (Lines 1281-1474) - Checkout form and summary

### 4. Responsive Design (Lines 1476-1734)
Comprehensive responsive breakpoints:
- Large Desktop (1200px+)
- Tablet (768px-1024px)
- Mobile (768px and below)
- Small Mobile (480px and below)

### 5. Accessibility & Special Features (Lines 1872-1965)
- Dark theme support
- Print styles
- High contrast mode
- Reduced motion support
- Focus styles for accessibility

## Key Features

### Azure Blue Blinking Effects
The signature azure blue blinking animation is applied to various elements:
- Navigation logo
- Page titles
- Important buttons
- Cart items on hover

### Smooth Animations
All interactive elements have smooth transitions and hover effects.

### Mobile-First Design
Responsive design ensures the site works perfectly on all devices.

## Customization Guide

### Changing Colors
Edit the CSS variables in the `:root` section:

```css
:root {
  --primary-blue: #your-color;
  --primary-purple: #your-color;
  /* etc. */
}
```

### Modifying Spacing
Adjust spacing variables:

```css
:root {
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  /* etc. */
}
```

### Adding New Components
Follow the existing naming convention:
- Use descriptive class names
- Group related styles together
- Add responsive variants if needed

### Customizing Animations
Modify keyframe animations at the bottom of the file or create new ones following the existing pattern.

## File Organization
- **Single CSS file**: All styles in `src/index.css`
- **No build process**: Pure CSS, no preprocessing needed
- **No external dependencies**: Removed Tailwind CSS completely
- **Easy maintenance**: Clear structure and comments

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- CSS Grid and Flexbox support required
- CSS custom properties (variables) support required

## Performance
- Optimized CSS with minimal redundancy
- Efficient selectors
- Hardware-accelerated animations where appropriate
- Print styles to reduce ink usage when printing

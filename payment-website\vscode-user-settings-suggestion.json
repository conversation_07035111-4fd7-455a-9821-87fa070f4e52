{"// NOTE": "These are suggested user-level settings for VS Code to better support Tailwind CSS globally", "// INSTRUCTIONS": "Copy these settings to your VS Code User Settings (File > Preferences > Settings > Open Settings JSON)", "css.validate": false, "less.validate": false, "scss.validate": false, "css.lint.unknownAtRules": "ignore", "css.lint.invalidApply": "ignore", "tailwindCSS.includeLanguages": {"css": "css", "scss": "scss", "sass": "scss"}, "tailwindCSS.experimental.classRegex": [["@apply\\s+([^;]+);", 1], ["className\\s*=\\s*[\"']([^\"']*)[\"']", 1], ["class\\s*=\\s*[\"']([^\"']*)[\"']", 1]], "emmet.includeLanguages": {"css": "css", "scss": "scss"}, "[css]": {"editor.quickSuggestions": {"other": true, "comments": false, "strings": true}}, "[scss]": {"editor.quickSuggestions": {"other": true, "comments": false, "strings": true}}}
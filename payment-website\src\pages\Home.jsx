import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { products } from '../data/products';
import { useCart } from '../context/CartContext';

const Home = () => {
  const { addToCart } = useCart();
  const [isVisible, setIsVisible] = useState(false);
  const [featuredProducts] = useState(products.slice(0, 3));

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleAddToCart = (product) => {
    addToCart(product);
    // Create a more elegant notification instead of alert
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 5rem;
      right: 1rem;
      background: #10b981;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      z-index: 50;
      animation: slide-in-right 0.3s ease-out;
    `;
    notification.textContent = `${product.name} added to cart!`;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  };

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        {/* Background Pattern */}
        <div className="hero-background">
          <div className="hero-gradient-overlay"></div>
          <div className="hero-pattern"></div>
        </div>

        <div className={`hero-content ${isVisible ? 'animate-fade-in-up' : ''}`} style={{opacity: isVisible ? 1 : 0}}>
          <h1 className="hero-title">
            Welcome to{' '}
            <span className="gradient-text">
              PayShop
            </span>
          </h1>

          <p className="hero-subtitle">
            Discover amazing products at unbeatable prices
          </p>

          <div className="hero-price-badge">
            <p className="hero-price-text">
              All items just ₹12 each!
            </p>
          </div>

          <div className="hero-buttons">
            <Link to="/shop" className="hero-button">
              Shop Now
            </Link>

            <Link to="/cart" className="glass-effect text-white font-semibold py-4 px-8 rounded-xl hover:bg-white/20 transition-all duration-300 border border-white/30">
              View Cart
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section style={{padding: '5rem 0', background: 'linear-gradient(to bottom, #f9fafb, white)'}}>
        <div className="container">
          <div className="text-center" style={{marginBottom: '4rem'}}>
            <h2 className="gradient-text font-bold" style={{fontSize: 'clamp(2rem, 5vw, 3rem)', marginBottom: '1rem'}}>
              Featured Products
            </h2>
            <p style={{fontSize: '1.25rem', color: '#666', maxWidth: '32rem', margin: '0 auto'}}>
              Handpicked items that our customers love the most
            </p>
          </div>

          <div className="products-grid" style={{marginBottom: '3rem'}}>
            {featuredProducts.map((product, index) => (
              <div
                key={product.id}
                className="card-product animate-fade-in-up"
                style={{animationDelay: `${index * 0.2}s`}}
              >
                <div className="product-image">
                  <img
                    src={product.image}
                    alt={product.name}
                  />
                  <div className="product-overlay">
                    <button
                      onClick={() => handleAddToCart(product)}
                      className="quick-add-btn"
                    >
                      Quick Add
                    </button>
                  </div>
                </div>

                <div className="product-info">
                  <h3>{product.name}</h3>
                  <p className="product-description">{product.description}</p>

                  <div className="product-footer">
                    <span className="price">₹{product.price}</span>
                    <button
                      onClick={() => handleAddToCart(product)}
                      className="add-to-cart-btn"
                    >
                      Add to Cart
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Link to="/shop" className="btn-azure" style={{fontSize: '1.125rem', padding: '1rem 2rem', display: 'inline-flex', alignItems: 'center', gap: '0.5rem'}}>
              <span>View All Products</span>
              <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section style={{padding: '5rem 0', background: 'linear-gradient(to bottom, white, #f9fafb)'}}>
        <div className="container">
          <div className="text-center" style={{marginBottom: '4rem'}}>
            <h2 className="gradient-text font-bold" style={{fontSize: 'clamp(2rem, 5vw, 3rem)', marginBottom: '1rem'}}>
              Why Choose PayShop?
            </h2>
            <p style={{fontSize: '1.25rem', color: '#666', maxWidth: '32rem', margin: '0 auto'}}>
              Experience the best shopping with our premium features
            </p>
          </div>

          <div style={{display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '2rem'}}>
            <div className="card-premium text-center" style={{padding: '2rem'}}>
              <div style={{
                width: '4rem',
                height: '4rem',
                background: 'linear-gradient(to bottom right, #3b82f6, #2563eb)',
                borderRadius: '1rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1.5rem auto',
                transition: 'transform 0.3s'
              }}>
                <svg width="32" height="32" fill="none" stroke="white" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 style={{fontSize: '1.25rem', fontWeight: '700', color: '#1f2937', marginBottom: '1rem'}}>Fast Delivery</h3>
              <p style={{color: '#666'}}>Quick and reliable shipping to your doorstep with real-time tracking</p>
            </div>

            <div className="card-premium text-center" style={{padding: '2rem'}}>
              <div style={{
                width: '4rem',
                height: '4rem',
                background: 'linear-gradient(to bottom right, #10b981, #059669)',
                borderRadius: '1rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1.5rem auto',
                transition: 'transform 0.3s'
              }}>
                <svg width="32" height="32" fill="none" stroke="white" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 style={{fontSize: '1.25rem', fontWeight: '700', color: '#1f2937', marginBottom: '1rem'}}>Secure Payment</h3>
              <p style={{color: '#666'}}>Safe and secure payment processing with bank-level encryption</p>
            </div>

            <div className="card-premium text-center" style={{padding: '2rem'}}>
              <div style={{
                width: '4rem',
                height: '4rem',
                background: 'linear-gradient(to bottom right, #8b5cf6, #7c3aed)',
                borderRadius: '1rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1.5rem auto',
                transition: 'transform 0.3s'
              }}>
                <svg width="32" height="32" fill="none" stroke="white" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <h3 style={{fontSize: '1.25rem', fontWeight: '700', color: '#1f2937', marginBottom: '1rem'}}>Easy Returns</h3>
              <p style={{color: '#666'}}>Hassle-free returns within 30 days with full money-back guarantee</p>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="hero-gradient" style={{padding: '5rem 0'}}>
        <div className="container text-center" style={{maxWidth: '64rem'}}>
          <h2 className="text-white font-bold" style={{fontSize: 'clamp(2rem, 5vw, 2.5rem)', marginBottom: '1rem'}}>
            Stay Updated with PayShop
          </h2>
          <p className="text-white" style={{fontSize: '1.25rem', opacity: 0.9, marginBottom: '2rem', maxWidth: '32rem', margin: '0 auto 2rem auto'}}>
            Subscribe to our newsletter and get exclusive deals, new product updates, and special offers
          </p>

          <div style={{display: 'flex', flexDirection: 'column', gap: '1rem', maxWidth: '28rem', margin: '0 auto'}}>
            <input
              type="email"
              placeholder="Enter your email"
              style={{
                flex: 1,
                padding: '1rem 1.5rem',
                borderRadius: '0.75rem',
                border: 'none',
                fontSize: '1rem',
                color: '#1f2937'
              }}
            />
            <button className="btn-azure" style={{padding: '1rem 2rem', whiteSpace: 'nowrap'}}>
              Subscribe
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;

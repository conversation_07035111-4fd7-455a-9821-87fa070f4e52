import { Link, useLocation } from 'react-router-dom';
import { useCart } from '../context/CartContext';

const Navbar = () => {
  const { getCartItemsCount } = useCart();
  const location = useLocation();

  const totalItems = getCartItemsCount();

  const isActive = (path) => location.pathname === path;

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <div className="navbar-content">
          {/* Desktop Navigation with Logo */}
          <div className="navbar-menu-pill">
            {/* Logo inside pill */}
            <Link to="/" className="navbar-logo-pill">
              <div className="logo-icon-pill">
                <span>P</span>
              </div>
              <span className="logo-text-pill">PayShop</span>
            </Link>

            {/* Navigation divider */}
            <div className="nav-divider"></div>

            {/* Navigation Links */}
            <Link
              to="/"
              className={`navbar-pill-link ${isActive('/') ? 'active' : ''}`}
            >
              <span>Home</span>
              {isActive('/') && (
                </div>
              )}
            </Link>

            <Link
              to="/shop"
              className={`navbar-pill-link ${isActive('/shop') ? 'active' : ''}`}
            >
              <span>Products</span>
            </Link>

            <Link
              to="/cart"
              className={`navbar-pill-link ${isActive('/cart') ? 'active' : ''}`}
            >
              <span>Cart</span>
              {totalItems > 0 && !isActive('/cart') && (
                <div className="cart-count-pill">
                  <span>{totalItems}</span>
                </div>
              )}
              {isActive('/cart') && (
                <div className="pill-badge">
                  <span>{totalItems || '0'}</span>
                </div>
              )}
            </Link>
          </div>

        </div>
      </div>
    </nav>
  );
};

export default Navbar;

# PayShop - E-commerce Payment Website

A modern React-based e-commerce website with shopping cart and checkout functionality.

## Features

- **Home Page**: Welcome page with featured products and company features
- **Shop Page**: Browse all 5 products (₹12 each) with add to cart functionality
- **Cart Page**: Manage cart items with quantity controls and remove options
- **Checkout Page**: Complete payment form with order summary
- **Responsive Design**: Works on desktop and mobile devices
- **Azure Blue Theme**: Beautiful blue gradient design with subtle animations

## Products Available

All products are priced at ₹12 each:
1. Premium Wireless Headphones
2. Smart Fitness Tracker
3. Portable Bluetooth Speaker
4. Wireless Phone Charger
5. USB-C Cable Set

## Technologies Used

- React 19.1.0
- React Router DOM 6.28.0
- Vite (for fast development)
- CSS3 with animations and gradients
- Context API for state management

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Navigate to the project directory:
   ```bash
   cd payment-website
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and visit `http://localhost:5173`

### Alternative (Windows)
Double-click the `run-dev.bat` file to automatically install dependencies and start the server.

## Project Structure

```
payment-website/
├── src/
│   ├── components/
│   │   ├── Navbar.jsx
│   │   └── Navbar.css
│   ├── pages/
│   │   ├── Home.jsx & Home.css
│   │   ├── Shop.jsx & Shop.css
│   │   ├── Cart.jsx & Cart.css
│   │   └── Checkout.jsx & Checkout.css
│   ├── context/
│   │   └── CartContext.jsx
│   ├── data/
│   │   └── products.js
│   ├── App.jsx
│   ├── App.css
│   └── main.jsx
├── public/
├── package.json
└── README.md
```

## Features in Detail

### Cart Management
- Add products to cart
- Update quantities
- Remove items
- Clear entire cart
- Persistent cart state across pages

### Checkout Process
- Shipping information form
- Payment details form
- Order summary
- Form validation
- Simulated payment processing

### Design Features
- Azure blue color scheme with gradients
- Smooth animations and hover effects
- Responsive grid layouts
- Modern card-based design
- Subtle blinking effects on interactive elements

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
